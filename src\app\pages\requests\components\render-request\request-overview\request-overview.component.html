<!-- Simple Request Overview -->
<div class="card border-0 shadow-sm mb-5">
  <!-- Simple Header -->
  <!-- <div class="card-header bg-light py-3 border-bottom">
    <h6 class="text-gray-800 mb-0 fw-bold">
      <i class="ki-duotone ki-document-text fs-6 text-primary me-2">
        <span class="path1"></span>
        <span class="path2"></span>
      </i>
      Request Overview
    </h6>
  </div> -->

  <div class="card-body p-4">
    <!-- Basic Information -->
    <div class="mb-5 ">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-information-5 fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        Basic Information
      </h6>

      <div class="rounded p-3">
        <div class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Operation Type:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.type }}</span>
        </div>
        <div class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Specialization:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.specializationScope }}</span>
        </div>
        <div class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Type:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.unit }}</span>
        </div>
      </div>
    </div>

    <!-- Location Information -->
    <div class="mb-5" *ngIf="request?.locations && request?.locations.length > 0">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-geolocation fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Location Details
      </h6>

      <div class="rounded p-3 mb-3">
        <div class="row g-3">
          <div *ngFor="let cityItem of request?.locations">
            <!-- City -->
            <div class="mb-2">
              <span class="text-gray-600 fs-6 fw-normal">City:</span>
              <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ cityItem.city.name_en }} | {{
                cityItem.city.name_ar }}</span>
            </div>

            <div *ngFor="let areaItem of cityItem.areas">
              <!-- Area -->
              <div class="mb-2">
                <span class="text-gray-600 fs-6 fw-normal">Area:</span>
                <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ areaItem.area.name_en }} | {{
                  areaItem.area.name_ar }}</span>
              </div>

              <!-- Subarea (only if exists) -->
              <div *ngFor="let subarea of areaItem.sub_areas" class="mb-2">
                <span class="text-gray-600 fs-6 fw-normal">Subarea:</span>
                <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ subarea.name_en }} | {{ subarea.name_ar }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Additional Address Info -->
    <div class="mb-5"
      *ngIf="request?.detailedAddress || request?.attributes?.detailedAddress || request?.attributes?.addressLink">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-map fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        Address Information
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.detailedAddress || request?.attributes?.detailedAddress" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Detailed Address:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.detailedAddress ||
            request?.attributes?.detailedAddress }}</span>
        </div>

        <div *ngIf="request?.attributes?.addressLink" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Address Link:</span>
          <a [href]="request.attributes.addressLink" target="_blank" rel="noopener noreferrer"
            class="text-primary fs-5 fw-semibold ms-2 text-hover-dark">View on Map</a>
        </div>
      </div>
    </div>

    <!-- Property Details -->
    <div class="mb-5"
      *ngIf="request?.attributes?.mallName || request?.attributes?.unitNumber || request?.attributes?.unitView || request?.attributes?.floor || request?.attributes?.buildingNumber || request?.attributes?.unitArea || request?.attributes?.rooms || request?.attributes?.bathRooms || request?.attributes?.gardenArea || request?.attributes?.unitFacing || request?.attributes?.floorNumber">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-home-2 fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Property Details
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.attributes?.mallName" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Mall Name:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.mallName }}</span>
        </div>
        <div *ngIf="request?.attributes?.buildingNumber" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Building Number:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.buildingNumber }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitNumber" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Number:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitNumber }}</span>
        </div>
        <div *ngIf="request?.attributes?.floor" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Floor:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.floor }}</span>
        </div>
        <div *ngIf="request?.attributes?.floorNumber" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Floor Number:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.floorNumber }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitArea" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Area:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitArea }} m²</span>
        </div>
        <div *ngIf="request?.attributes?.rooms" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Number of Rooms:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.rooms }}</span>
        </div>
        <div *ngIf="request?.attributes?.bathRooms" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Number of Bathrooms:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.bathRooms }}</span>
        </div>
        <div *ngIf="request?.attributes?.gardenArea" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Garden Area:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.gardenArea }} m²</span>
        </div>
        <div *ngIf="request?.attributes?.unitFacing" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Facing:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitFacing }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitView" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit View:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitView }}</span>
        </div>
      </div>
    </div>

    <!-- Status Information -->
    <div class="mb-5"
      *ngIf="request?.attributes?.deliveryStatus || request?.attributes?.financialStatus || request?.attributes?.finishingStatus || request?.attributes?.legalStatus || request?.attributes?.deliveryDate">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-status fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Status Information
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.attributes?.deliveryStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Delivery Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.deliveryStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.deliveryDate" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Delivery Date:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.deliveryDate }}</span>
        </div>
        <div *ngIf="request?.attributes?.finishingStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Finishing Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.finishingStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.legalStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Legal Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.legalStatus }}</span>
        </div>
        <div *ngIf="request?.attributes?.financialStatus" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Financial Status:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.financialStatus }}</span>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="mb-5" *ngIf="request?.attributes?.otherAccessories">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-element-plus fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
          <span class="path4"></span>
          <span class="path5"></span>
        </i>
        Additional Information
      </h6>

      <div class="rounded p-3">
        <span class="text-gray-600 fs-6 fw-normal">Other Accessories:</span>
        <div class="d-inline-flex flex-wrap gap-2 ms-2">
          <span *ngFor="let item of request?.attributes?.otherAccessories"
            class="badge badge-light-primary fs-8 fw-semibold px-2 py-1">
            {{ item }}
          </span>
        </div>
      </div>
    </div>

    <!-- Notes & Financial Information -->
    <div class="mb-5"
      *ngIf="request?.attributes?.notes || request?.attributes?.paymentMethod || request?.attributes?.unitPrice || request?.attributes?.unitPriceSuggestions">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-dollar fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        Financial & Notes
      </h6>

      <div class="rounded p-3">
        <div *ngIf="request?.attributes?.paymentMethod" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Payment Method:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.paymentMethod }}</span>
        </div>
        <div *ngIf="request?.attributes?.unitPrice" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Price:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.unitPrice }} EGP</span>
        </div>
        <div *ngIf="request?.attributes?.unitPriceSuggestions" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Unit Price Suggestion:</span>
          <span class="badge fs-6 fw-semibold ms-2" [ngClass]="{
            'badge-light-success': request?.attributes?.unitPriceSuggestions === 1,
            'badge-light-danger': request?.attributes?.unitPriceSuggestions !== 1
          }">
            {{ (request?.attributes?.unitPriceSuggestions === 1 ? 'APPROVED' : 'PENDING') }}
          </span>
        </div>
        <div *ngIf="request?.attributes?.notes" class="mb-2">
          <span class="text-gray-600 fs-6 fw-normal">Notes:</span>
          <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.attributes?.notes }}</span>
        </div>
      </div>
    </div>

    <!-- Media Gallery -->
    <div class="mb-5"
      *ngIf="request?.mainImage || (request?.gallery && request?.gallery?.length > 0) || request?.unitInMasterPlanImage">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-picture fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        Media Gallery
      </h6>

      <div class="rounded p-3">
        <!-- Main Image -->
        <div class="mb-2 d-flex align-items-center" *ngIf="request?.mainImage">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-picture fs-6 text-primary me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Main Image :
          </span>
          <img [src]="request?.mainImage" alt="Main Image" class="rounded cursor-pointer shadow-sm"
            style="height: 30px; width: 50px; object-fit: cover;"
            (click)="openImageModal(request?.mainImage, 'Main Image')" data-bs-toggle="modal"
            data-bs-target="#mediaModal">
        </div>

        <!-- Gallery Media (Images and Videos) -->
        <div class="mb-2 d-flex align-items-center" *ngIf="request?.gallery?.length > 0">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-gallery fs-6 text-info me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Gallery Media :
          </span>
          <div class="d-flex gap-2">
            <ng-container *ngFor="let media of request?.gallery; let i = index">
              <!-- Image -->
              <img *ngIf="media.type === 'image'" [src]="media.url" [alt]="'Gallery Image ' + (i + 1)"
                class="rounded cursor-pointer shadow-sm" style="height: 30px; width: 50px; object-fit: cover;"
                (click)="openImageModal(media.url, 'Gallery Image ' + (i + 1))" data-bs-toggle="modal"
                data-bs-target="#mediaModal">

              <!-- Video -->
              <div *ngIf="media.type === 'video'" class="position-relative rounded cursor-pointer shadow-sm"
                style="height: 30px; width: 50px; background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA1MCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0yMCAxMEwyOCAxNUwyMCAyMFYxMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=') center/cover; background-color: #374151;"
                (click)="openVideoModal(media.url, 'Gallery Video ' + (i + 1))" data-bs-toggle="modal"
                data-bs-target="#mediaModal">
                <div class="position-absolute top-50 start-50 translate-middle">
                  <i class="ki-duotone ki-play-circle fs-4 text-white opacity-75">
                    <span class="path1"></span>
                    <span class="path2"></span>
                  </i>
                </div>
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Unit in Master Plan Image -->

        <div class="mb-2 d-flex align-items-center" *ngIf="request?.unitInMasterPlanImage">
          <span class="text-gray-600 fs-6 me-3" style="min-width: 120px;">
            <i class="ki-duotone ki-design-frame fs-6 text-success me-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Unit Plan :
          </span>
          <img [src]="request?.unitInMasterPlanImage" alt="Unit in Master Plan" class="rounded cursor-pointer shadow-sm"
            style="height: 30px; width: 50px; object-fit: cover;"
            (click)="openImageModal(request?.unitInMasterPlanImage, 'Unit in Master Plan')" data-bs-toggle="modal"
            data-bs-target="#mediaModal">
        </div>
      </div>
    </div>

    <!-- Created At -->
    <div class="mb-4">
      <h6 class="text-primary mb-3 fw-bold bg-gray-100 rounded p-3">
        <i class="ki-duotone ki-calendar-2 fs-6 text-primary me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
          <span class="path4"></span>
        </i>
        Request Information
      </h6>

      <div class="rounded p-3">
        <span class="text-gray-600 fs-6 fw-normal">Created At:</span>
        <span class="text-gray-800 fs-5 fw-semibold ms-2">{{ request?.createdAt }}</span>
      </div>
    </div>
  </div>
</div>

<!-- Media Modal -->
<div class="modal fade" id="mediaModal" tabindex="-1" aria-labelledby="mediaModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="mediaModalLabel">{{ modalTitle }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <!-- Image Display -->
        <img *ngIf="modalType === 'image'" [src]="modalContent" [alt]="modalTitle" class="img-fluid rounded shadow"
          style="max-height: 300px;">

        <!-- Video Display -->
        <video *ngIf="modalType === 'video'" [src]="modalContent" controls class="w-100 rounded shadow"
          style="max-height: 500px;">
          Your browser does not support the video tag.
        </video>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>

      </div>
    </div>
  </div>
</div>