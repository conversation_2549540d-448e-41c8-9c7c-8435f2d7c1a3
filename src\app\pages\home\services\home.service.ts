import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { AbstractCrudService } from '../../shared/services/abstract-crud.service';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ContractService extends AbstractCrudService {
  apiUrl = `${environment.apiUrl}/unit/advertisement-shuffle`;
   getFeaturedProperties(limit: number = 4, offset: number = 0): Observable<any> {
    const params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());

    return this.http.get<any>(`${this.apiUrl}/unit/advertisement-shuffle`, { params });

   }



}
