import { Component, OnInit, HostListener, After<PERSON>iewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { AuthenticationService } from '../authentication/services/authentication.service';
import { Router } from '@angular/router';

declare var bootstrap: any;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  currentUser: any = null;
  isLoggedIn: boolean = false;
  showUserDropdown: boolean = false;
  showMobileMenu: boolean = false;

  // Location Carousel Data
  locationSlides: any[][] = [];
  currentSlideIndex: number = 0;
  carouselInterval: any;

  // Articles Carousel Data
  currentArticleSlideIndex: number = 0;
  articlesCarouselInterval: any;
  locations: any[] = [
    {
      id: 1,
      name: ' New Cairo',
      image: './assets/media/stock/600x400/img-10.jpg',
      propertyCount: 2341
    },
    {
      id: 2,
      name: '  <PERSON><PERSON>',
      image: './assets/media/stock/600x400/img-20.jpg',
      propertyCount: 1234
    },
    {
      id: 3,
      name: ' Sheikh Zayed  ',
      image: './assets/media/stock/600x400/img-30.jpg',
      propertyCount: 3421
    },
    {
      id: 4,
      name: '   Heliopolis',
      image: './assets/media/stock/600x400/img-40.jpg',
      propertyCount: 2341
    },
    {
      id: 5,
      name: '   Nasr City',
      image: './assets/media/stock/600x400/img-50.jpg',
      propertyCount: 987
    },
    {
      id: 6,
      name: '  6 October',
      image: './assets/media/stock/600x400/img-60.jpg',
      propertyCount: 1543
    },
    {
      id: 7,
      name: '  Maadi',
      image: './assets/media/stock/600x400/img-70.jpg',
      propertyCount: 876
    },
    {
      id: 8,
      name: '  Zamalek',
      image: './assets/media/stock/600x400/img-80.jpg',
      propertyCount: 654
    },
    {
      id: 9,
      name: '  New Cairo',
      image: './assets/media/stock/600x400/img-90.jpg',
      propertyCount: 1098
    },
    {
      id: 10,
      name: '  Nasr City',
      image: './assets/media/stock/600x400/img-100.jpg',
      propertyCount: 1432
    },
    {
      id: 11,
      name: '  Nasr City',
      image: './assets/media/stock/600x400/img-100.jpg',
      propertyCount: 1432
    },
    {
      id: 12,
      name: '  Nasr City',
      image: './assets/media/stock/600x400/img-100.jpg',
      propertyCount: 1432
    }
  ];

  constructor(private authService: AuthenticationService) { }

  ngOnInit(): void {
    this.checkUserSession();
    this.initializeLocationSlides();
  }

  ngAfterViewInit(): void {
    // Initialize Bootstrap carousels after view is loaded
    setTimeout(() => {
      this.initializeCarousel();
      this.initializeArticlesCarousel();
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up intervals when component is destroyed
    if (this.carouselInterval) {
      clearInterval(this.carouselInterval);
    }
    if (this.articlesCarouselInterval) {
      clearInterval(this.articlesCarouselInterval);
    }
  }

  checkUserSession(): void {
    // Check if user is logged in by checking localStorage
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');

    if (authToken && currentUser) {
      try {
        this.currentUser = JSON.parse(currentUser);
        this.isLoggedIn = true;
      } catch (error) {
        // If parsing fails, user is not logged in
        this.isLoggedIn = false;
        this.currentUser = null;
      }
    } else {
      this.isLoggedIn = false;
      this.currentUser = null;
    }
  }

  getUserDisplayName(): string {
    if (this.currentUser) {
      return this.currentUser.fullName  || 'User';
    }
    return 'Guest';
  }

  getUserProfileImage(): string {
    if (this.currentUser && this.currentUser.image) {
      return this.currentUser.image;
    }
    // Return default avatar if no profile image
    return 'assets/media/avatars/blank.png';
  }

  toggleUserDropdown(): void {
    this.showUserDropdown = !this.showUserDropdown;
  }

  closeUserDropdown(): void {
    this.showUserDropdown = false;
  }

  logout(): void {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    this.isLoggedIn = false;
    this.currentUser = null;
    this.showUserDropdown = false;
    // Optionally redirect to login page
    // this.router.navigate(['/authentication/login']);
  }

  toggleMobileMenu(): void {
    this.showMobileMenu = !this.showMobileMenu;
    // Close user dropdown when mobile menu is toggled
    if (this.showMobileMenu) {
      this.showUserDropdown = false;
    }
  }

  closeMobileMenu(): void {
    this.showMobileMenu = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const userProfile = target.closest('.user-profile');
    const userDropdown = target.closest('.user-dropdown');
    const navbarToggler = target.closest('.navbar-toggler');
    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');

    // Close user dropdown if clicked outside of user profile and dropdown
    if (!userProfile && !userDropdown && this.showUserDropdown) {
      this.showUserDropdown = false;
    }

    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown
    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {
      this.showMobileMenu = false;
    }
  }

  // Location Carousel Methods
  initializeLocationSlides(): void {
    // Split locations into slides of 5 items each
    const itemsPerSlide = 5;
    this.locationSlides = [];

    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {
      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));
    }
  }

  initializeCarousel(): void {
    try {
      const carouselElement = document.getElementById('horizontalCarousel');
      if (carouselElement) {
        // Try Bootstrap first
        if (typeof bootstrap !== 'undefined') {
          const carousel = new bootstrap.Carousel(carouselElement, {
            interval: 5000,
            ride: 'carousel',
            wrap: true,
            keyboard: true,
            pause: 'hover'
          });
          console.log('Bootstrap carousel initialized');
        } else {
          // Fallback: Manual carousel control
          this.startManualCarousel();
          console.log('Manual carousel initialized');
        }
      }
    } catch (error) {
      console.error('Error initializing carousel:', error);
      // Fallback to manual carousel
      this.startManualCarousel();
    }
  }

  startManualCarousel(): void {
    // Clear any existing interval
    if (this.carouselInterval) {
      clearInterval(this.carouselInterval);
    }

    // Start auto-play
    this.carouselInterval = setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide(): void {
    const totalSlides = this.locationSlides.length;
    if (totalSlides > 0) {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;
      this.updateCarouselDisplay();
    }
  }

  prevSlide(): void {
    const totalSlides = this.locationSlides.length;
    if (totalSlides > 0) {
      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;
      this.updateCarouselDisplay();
    }
  }

  updateCarouselDisplay(): void {
    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');
    carouselItems.forEach((item, index) => {
      if (index === this.currentSlideIndex) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }

  onLocationClick(location: any): void {
    console.log('Location clicked:', location);
    // Add your navigation logic here
    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });
  }

  // Method to load locations from API (for future integration)
  // loadLocations(): void {
  //   // Replace with actual API call
  //   // this.locationService.getLocations().subscribe(data => {
  //   //   this.locations = data;
  //   //   this.initializeLocationSlides();
  //   //   // Re-initialize carousel after data loads
  //   //   setTimeout(() => this.initializeCarousel(), 100);
  //   // });
  // }

  loadMoreLocations(){

  }

  // Articles Carousel Methods
  initializeArticlesCarousel(): void {
    try {
      const carouselElement = document.getElementById('articlesCarousel');
      if (carouselElement) {
        // Try Bootstrap first
        if (typeof bootstrap !== 'undefined') {
          const carousel = new bootstrap.Carousel(carouselElement, {
            interval: 6000,
            ride: 'carousel',
            wrap: true,
            keyboard: true,
            pause: 'hover'
          });
          console.log('Bootstrap articles carousel initialized');
        } else {
          // Fallback: Manual carousel control
          this.startManualArticlesCarousel();
          console.log('Manual articles carousel initialized');
        }
      }
    } catch (error) {
      console.error('Error initializing articles carousel:', error);
      // Fallback to manual carousel
      this.startManualArticlesCarousel();
    }
  }

  startManualArticlesCarousel(): void {
    // Clear any existing interval
    if (this.articlesCarouselInterval) {
      clearInterval(this.articlesCarouselInterval);
    }

    // Start auto-play
    this.articlesCarouselInterval = setInterval(() => {
      this.nextArticleSlide();
    }, 6000);
  }

  nextArticleSlide(): void {
    const totalSlides = 3; // We have 3 slides
    this.currentArticleSlideIndex = (this.currentArticleSlideIndex + 1) % totalSlides;
    this.updateArticlesCarouselDisplay();
  }

  prevArticleSlide(): void {
    const totalSlides = 3; // We have 3 slides
    this.currentArticleSlideIndex = this.currentArticleSlideIndex === 0 ? totalSlides - 1 : this.currentArticleSlideIndex - 1;
    this.updateArticlesCarouselDisplay();
  }

  updateArticlesCarouselDisplay(): void {
    const carouselItems = document.querySelectorAll('#articlesCarousel .carousel-item');
    const indicators = document.querySelectorAll('#articlesCarousel .carousel-indicators button');

    carouselItems.forEach((item, index) => {
      if (index === this.currentArticleSlideIndex) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });

    indicators.forEach((indicator, index) => {
      if (index === this.currentArticleSlideIndex) {
        indicator.classList.add('active');
      } else {
        indicator.classList.remove('active');
      }
    });
  }

  onSubscribeClick() {
    console.log('Subscribe button clicked');

  }

}
